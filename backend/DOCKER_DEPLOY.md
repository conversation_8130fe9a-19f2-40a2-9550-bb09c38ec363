# Adrian OA 后端 Docker 部署指南

## 快速部署

### 一键部署（推荐）

```bash
cd backend
chmod +x deploy.sh
./deploy.sh
```

部署脚本提供三种模式：

1. **智能部署**（默认）- 仅在代码变更时重新构建，节省时间
2. **强制重建** - 完全重新构建镜像，确保最新环境
3. **快速启动** - 使用现有镜像直接启动，最快速度

### 手动部署

```bash
cd backend

# 构建并启动
docker-compose up -d --build

# 查看状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

## 部署说明

### 系统要求

-   Ubuntu 18.04+
-   Docker 20.0+
-   Docker Compose 1.25+

### 端口配置

-   后端服务：18080

### 数据持久化

-   `./uploads` - 文件上传目录
-   `./data` - 数据文件目录

### 常用命令

```bash
# 启动服务
docker-compose up -d

# 停止服务
docker-compose down

# 重启服务
docker-compose restart

# 查看日志
docker-compose logs -f

# 进入容器
docker-compose exec backend bash

# 更新代码后重新部署
docker-compose down
docker-compose up -d --build
```

### 访问地址

-   API 文档：http://服务器 IP:18080/docs
-   后端服务：http://服务器 IP:18080

## 优化特性

### 🚀 加速优化

-   **阿里云镜像源**：使用阿里云 apt 和 pip 镜像源，大幅提升下载速度
-   **智能构建**：自动检测代码变更，避免不必要的重复构建
-   **分层缓存**：优化 Docker 构建缓存，提升构建效率

### 🔍 智能检查

-   **固定端口配置**：使用固定端口 18080，避免端口冲突
-   **资源重复检查**：避免重复创建镜像和容器
-   **服务健康检查**：自动验证服务启动状态

### 📊 部署模式

-   **智能部署**：根据代码变更情况自动选择构建策略
-   **强制重建**：确保环境完全更新
-   **快速启动**：最快速度启动现有服务

### 故障排除

1. **端口占用**：脚本使用固定端口 18080，避免冲突
2. **构建失败**：检查网络连接，脚本已配置阿里云镜像源
3. **OCR 功能异常**：确保系统依赖正确安装
4. **镜像过多**：脚本提供清理选项，释放磁盘空间

### 生产环境建议

1. 使用反向代理（如 Nginx）
2. 配置防火墙规则
3. 定期备份数据目录
4. 监控容器资源使用情况
5. 定期清理未使用的镜像
