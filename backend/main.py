# -*- coding: utf-8 -*-
import os
import logging
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from api import report, routes, settings, invoice

# 配置日志
logging.basicConfig(level=logging.INFO)

# 获取环境变量
ENVIRONMENT = os.getenv("ENVIRONMENT", "development")
DEBUG = os.getenv("DEBUG", "True").lower() == "true"

# CORS 配置 - 内网环境允许所有来源
CORS_ORIGINS = ["*"]

app = FastAPI(
    title="AI智能化管理系统",
    description="集成考勤管理、发票识别OCR、数据处理的智能管理系统",
    version="1.0.0",
    debug=DEBUG
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE"],
    allow_headers=["*"],
)

# 注册路由
app.include_router(routes.router, prefix="/api", tags=["Excel处理"])
app.include_router(report.router, prefix="/api/report", tags=["报表生成"])
app.include_router(settings.router, prefix="/api/settings", tags=["系统设置"])
app.include_router(invoice.router, prefix="/api/invoice", tags=["发票处理"])

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=int(os.getenv("PORT", 18080)),
        reload=DEBUG,
        log_level="info"
    )
