#!/bin/bash

# Adrian OA 后端一键部署脚本
# 适用于 Ubuntu 服务器

set -e  # 遇到错误立即退出

# 配置变量
PROJECT_NAME="adrian-oa-backend"
DEFAULT_PORT=18080
COMPOSE_FILE="docker-compose.yml"

echo "=== Adrian OA 后端部署开始 ==="



# 函数：检查镜像是否存在
check_image_exists() {
    local image_name=$1
    if docker images | grep -q "$image_name"; then
        return 0  # 镜像存在
    else
        return 1  # 镜像不存在
    fi
}

# 函数：检查代码是否有变更
check_code_changes() {
    local image_name=$1
    if check_image_exists "$image_name"; then
        # 检查最近的代码修改时间（包括Python文件、requirements.txt和Dockerfile）
        local last_modified=$(find . -type f -name "*.py" -o -name "requirements.txt" -o -name "Dockerfile" | xargs stat -c %Y 2>/dev/null | sort -n | tail -1)
        local image_created=$(docker inspect --format='{{.Created}}' "$image_name" 2>/dev/null | xargs date -d | date +%s)

        if [ "$last_modified" -gt "$image_created" ]; then
            return 0  # 有变更
        else
            return 1  # 无变更
        fi
    else
        return 0  # 镜像不存在，需要构建
    fi
}

# 检查必要工具是否安装
echo "检查系统环境..."

# 检查 Docker 是否安装
if ! command -v docker &> /dev/null; then
    echo "Docker 未安装，正在安装..."
    sudo apt-get update
    sudo apt-get install -y docker.io docker-compose
    sudo systemctl start docker
    sudo systemctl enable docker
    sudo usermod -aG docker $USER
    echo "Docker 安装完成，请重新登录后再次运行此脚本"
    exit 1
fi

# 检查 Docker Compose 是否安装
if ! command -v docker-compose &> /dev/null; then
    echo "Docker Compose 未安装，正在安装..."
    sudo apt-get install -y docker-compose
fi

echo "使用端口 $DEFAULT_PORT"

# 获取镜像名称
IMAGE_NAME="${PROJECT_NAME}"

# 停止现有容器
echo "停止现有容器..."
docker-compose down 2>/dev/null || true

# 自动判断部署模式
echo "=== 自动判断部署模式 ==="
if check_image_exists "$IMAGE_NAME"; then
    if check_code_changes "$IMAGE_NAME"; then
        echo "检测到代码变更，重新构建镜像..."
        docker-compose up -d --build
    else
        echo "代码无变更，使用现有镜像启动..."
        docker-compose up -d
    fi
else
    echo "镜像不存在，构建新镜像..."
    docker-compose up -d --build
fi

# 等待服务启动
echo "等待服务启动..."
sleep 8

# 检查服务状态
echo "检查服务状态..."
docker-compose ps

# 检查服务健康状态
echo "检查服务健康状态..."
if curl -f http://localhost:$DEFAULT_PORT/docs >/dev/null 2>&1; then
    echo "✅ 服务启动成功！"
else
    echo "⚠️  服务可能未完全启动，请查看日志"
fi

# 显示日志
echo "显示最近日志..."
docker-compose logs --tail=20

# 清理未使用的镜像（可选）
read -p "是否清理未使用的Docker镜像以释放空间？(y/N): " cleanup
if [[ $cleanup =~ ^[Yy]$ ]]; then
    echo "清理未使用的镜像..."
    docker image prune -f
fi

echo ""
echo "=== 部署完成 ==="
echo "🚀 服务地址: http://localhost:$DEFAULT_PORT"
echo "📖 API文档: http://localhost:$DEFAULT_PORT/docs"
echo "📋 查看日志: docker-compose logs -f"
echo "🛑 停止服务: docker-compose down"
echo "🔄 重启服务: docker-compose restart"
