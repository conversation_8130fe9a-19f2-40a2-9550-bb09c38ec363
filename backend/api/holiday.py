from fastapi import APIRouter, HTTPException
from typing import Dict, List, Any
from datetime import datetime, date, timedelta
from chinese_calendar import is_workday, is_holiday

router = APIRouter()

@router.get("/holidays")
async def get_holidays(year: int) -> Dict[str, List[str]]:
    """
    获取指定年份的节假日和调休工作日信息

    Args:
        year: 年份

    Returns:
        Dict[str, List[str]]: 包含节假日和调休工作日的字典
    """
    # 验证年份范围
    current_year = datetime.now().year
    if year < 2020 or year > current_year + 2:
        raise HTTPException(status_code=400, detail="年份超出支持范围")

    # 获取该年的所有日期
    start_date = date(year, 1, 1)
    end_date = date(year, 12, 31)
    delta = end_date - start_date

    holidays = []
    workdaysOnWeekends = []

    # 遍历该年的每一天
    for i in range(delta.days + 1):
        current_date = start_date + timedelta(days=i)
        date_str = current_date.strftime('%Y-%m-%d')
        weekday = current_date.weekday()

        try:
            # 判断是否为节假日（周一至周五的法定节假日）
            if is_holiday(current_date) and weekday < 5:
                holidays.append(date_str)

            # 判断是否为调休工作日（周末但需要上班）
            if is_workday(current_date) and weekday >= 5:
                workdaysOnWeekends.append(date_str)
        except Exception as e:
            # 如果出错，使用基本的周末判断
            if weekday >= 5:  # 周六和周日
                holidays.append(date_str)

    return {
        "holidays": holidays,
        "workdaysOnWeekends": workdaysOnWeekends
    }