# Python 3.11+ 兼容的依赖版本 - 固定版本与开发环境一致
# Web框架和服务器
fastapi==0.115.11
uvicorn==0.34.0
python-multipart==0.0.6

# 数据处理
pandas==2.2.3
openpyxl==3.1.5
xlsxwriter==3.2.5

# 日期处理
python-dateutil==2.9.0
chinese-calendar==1.10.0

# 工具库
pydantic==2.10.6

# OCR相关依赖 - 锁定到开发环境测试过的稳定版本
paddlepaddle==2.5.2
paddleocr==2.7.3
pytesseract==0.3.10
pdf2image==1.16.3
Pillow==11.3.0
opencv-python-headless==********

# 生产环境依赖
gunicorn==21.2.0
psutil==7.0.0

# 核心依赖
numpy==1.26.4