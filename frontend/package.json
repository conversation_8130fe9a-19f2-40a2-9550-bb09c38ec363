{"name": "attendance-management-system", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/", "test:unit": "vitest"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^1.8.1", "dayjs": "^1.11.13", "element-plus": "^2.9.5", "pinia": "^3.0.1", "vue": "^3.5.13", "vue-router": "^4.5.0", "xlsx": "^0.18.5"}, "devDependencies": {"@rushstack/eslint-patch": "^1.3.3", "@tsconfig/node18": "^18.2.2", "@types/jsdom": "^21.1.6", "@types/node": "^22.13.9", "@vitejs/plugin-vue": "^5.2.1", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/test-utils": "^2.4.4", "@vue/tsconfig": "^0.7.0", "eslint": "^8.49.0", "eslint-plugin-vue": "^9.17.0", "jsdom": "^24.0.0", "prettier": "^3.0.3", "sass": "^1.71.1", "typescript": "~5.7.2", "vite": "^5.1.4", "vitest": "^1.3.1", "vue-tsc": "^2.2.4"}}